﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class InitReportingSchema : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OverUnderEntry_EndDayReport_EndDayReportId",
                table: "OverUnderEntry");

            migrationBuilder.DropPrimaryKey(
                name: "PK_OverUnderEntry",
                table: "OverUnderEntry");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EndDayReport",
                table: "EndDayReport");

            migrationBuilder.EnsureSchema(
                name: "EoD");

            migrationBuilder.RenameTable(
                name: "OverUnderEntry",
                newName: "OverUnderEntrys",
                newSchema: "EoD");

            migrationBuilder.RenameTable(
                name: "EndDayReport",
                newName: "EndDayReports",
                newSchema: "EoD");

            migrationBuilder.RenameIndex(
                name: "IX_OverUnderEntry_EndDayReportId",
                schema: "EoD",
                table: "OverUnderEntrys",
                newName: "IX_OverUnderEntrys_EndDayReportId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_OverUnderEntrys",
                schema: "EoD",
                table: "OverUnderEntrys",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EndDayReports",
                schema: "EoD",
                table: "EndDayReports",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_OverUnderEntrys_EndDayReports_EndDayReportId",
                schema: "EoD",
                table: "OverUnderEntrys",
                column: "EndDayReportId",
                principalSchema: "EoD",
                principalTable: "EndDayReports",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OverUnderEntrys_EndDayReports_EndDayReportId",
                schema: "EoD",
                table: "OverUnderEntrys");

            migrationBuilder.DropPrimaryKey(
                name: "PK_OverUnderEntrys",
                schema: "EoD",
                table: "OverUnderEntrys");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EndDayReports",
                schema: "EoD",
                table: "EndDayReports");

            migrationBuilder.RenameTable(
                name: "OverUnderEntrys",
                schema: "EoD",
                newName: "OverUnderEntry");

            migrationBuilder.RenameTable(
                name: "EndDayReports",
                schema: "EoD",
                newName: "EndDayReport");

            migrationBuilder.RenameIndex(
                name: "IX_OverUnderEntrys_EndDayReportId",
                table: "OverUnderEntry",
                newName: "IX_OverUnderEntry_EndDayReportId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_OverUnderEntry",
                table: "OverUnderEntry",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EndDayReport",
                table: "EndDayReport",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_OverUnderEntry_EndDayReport_EndDayReportId",
                table: "OverUnderEntry",
                column: "EndDayReportId",
                principalTable: "EndDayReport",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
