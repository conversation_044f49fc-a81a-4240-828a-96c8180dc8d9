﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class AddNotesToEndDayReport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.RenameColumn(
            //    name: "Amount",
            //    schema: "EoD",
            //    table: "OverUnderEntrys",
            //    newName: "OverAmount");

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                schema: "EoD",
                table: "EndDayReports",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Notes",
                schema: "EoD",
                table: "EndDayReports");

            //migrationBuilder.RenameColumn(
            //    name: "OverAmount",
            //    schema: "EoD",
            //    table: "OverUnderEntrys",
            //    newName: "Amount");
        }
    }
}
