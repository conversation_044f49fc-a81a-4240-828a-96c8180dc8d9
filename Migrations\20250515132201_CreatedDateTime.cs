﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class CreatedDateTime : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalCredits",
                table: "EndDayReport");

            migrationBuilder.RenameColumn(
                name: "CreatedAt",
                table: "EndDayReport",
                newName: "CreatedDateTime");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CreatedDateTime",
                table: "EndDayReport",
                newName: "CreatedAt");

            migrationBuilder.AddColumn<decimal>(
                name: "TotalCredits",
                table: "EndDayReport",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }
    }
}
