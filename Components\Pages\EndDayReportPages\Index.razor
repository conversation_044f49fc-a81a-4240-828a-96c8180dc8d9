﻿@page "/"
@page "/enddayreports"
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.QuickGrid
@using EndDayReports.model
@using EndDayReports.Data
@using System.Globalization

@inject IDbContextFactory<EndDayReports.Data.EndDayReportsContext> DbFactory

<PageTitle>End Day Reports</PageTitle>

<h1 class="mb-4">End Day Reports</h1>

<p>
	<a class="btn btn-primary mb-3" href="enddayreports/create">Create New</a>
</p>

<div class="page-size-chooser">
	Items per page:
	<select @bind="@pagination.ItemsPerPage">
		<option>5</option>
		<option>10</option>
		<option>20</option>
		<option>50</option>
	</select>
</div>

@if (_isLoading)
{
	<p><em>Loading...</em></p>
}
else
{
	<div class="quickgrid" tabindex="-1">
		<QuickGrid TGridItem="EndDayReport"
				   ItemsProvider="LoadEndDayReports"
				   Pagination="@pagination"
				   Class="table table-striped table-bordered table-hover table-sm table-responsive"
				   >

			<PropertyColumn Property="enddayreport => enddayreport.CreatedDateTime" Title="Created Date/Time">
				<HeaderTemplate>
					<span class="text-start fw-bold">Created Date/Time</span>
				</HeaderTemplate>
			</PropertyColumn>
			
			<TemplateColumn Context="report" Title="Created By">
				<HeaderTemplate>
					<span class="text-start w-100 fw-bold">Created By</span>
				</HeaderTemplate>
				<ChildContent>
					<div class="text-start">
						@report.CreatedByEmployeeName
					</div>
				</ChildContent>
			</TemplateColumn>

			<TemplateColumn Context="report" Title="Total Sales">
				<HeaderTemplate>
					<span class="text-end w-100 fw-bold">Product Total Sales</span>
				</HeaderTemplate>
				<ChildContent>
					<div class="text-end">
						@report.GrandTotal.ToString("C")
					</div>
				</ChildContent>
			</TemplateColumn>

			<TemplateColumn Context="r" Title="Actions">
				<HeaderTemplate>
					<span class="text-center w-100 fw-bold">Actions</span>
				</HeaderTemplate>
				<ChildContent>
					<div class="text-center no-wrap">
						<a class="btn btn-sm btn-outline-primary me-1" href="@($"enddayreports/edit?id={r.Id}")">Edit</a>
						<a class="btn btn-sm btn-outline-info me-1" href="@($"enddayreports/details?id={r.Id}")">Details</a>
						<a class="btn btn-sm btn-outline-danger" href="@($"enddayreports/delete?id={r.Id}")">Delete</a>
					</div>
				</ChildContent>
			</TemplateColumn>
		</QuickGrid>
	</div>
	
	<Paginator State="@pagination" />
}
@code {
	PaginationState pagination = new PaginationState { ItemsPerPage = 10 };
	private bool _isLoading = true;


	protected override void OnInitialized()
	{
		_isLoading = false;
	}

	private async ValueTask<GridItemsProviderResult<EndDayReport>> LoadEndDayReports(GridItemsProviderRequest<EndDayReport> request)
	{
		using var context = DbFactory.CreateDbContext();

		var query = context.EndDayReport.AsQueryable();
		query = query.OrderByDescending(r => r.CreatedDateTime); // default sort


		var totalCount = await query.CountAsync();

		var data = await query
			.Skip(request.StartIndex)
			.Take(request.Count ?? 10)
			.ToListAsync();

		return GridItemsProviderResult.From(data, totalCount);
	}




}
