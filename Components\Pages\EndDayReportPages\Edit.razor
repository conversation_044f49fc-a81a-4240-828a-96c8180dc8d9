﻿@page "/enddayreports/edit"
@using Microsoft.EntityFrameworkCore
@using EndDayReports.model
@using System.Globalization
@inject IDbContextFactory<EndDayReports.Data.EndDayReportsContext> DbFactory
@inject NavigationManager NavigationManager
@inject IConfiguration Configuration
@inject IJSRuntime JS


<PageTitle>Edit Report</PageTitle>

<div class="container mt-4">
    <h1 class="mb-4">Edit Report</h1>

    @if (EndDayReport is null)
    {
        <p><em>Loading...</em></p>
    }
    @*  else if (errorMessages.Any())
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var error in errorMessages)
                {
                    <li>@error</li>
                }
            </ul>
        </div>
    } *@
    else
    {
        <EditForm method="post" Model="EndDayReport" OnValidSubmit="UpdateEndDayReport" OnInvalidSubmit="HandleInvalidSubmit" FormName="edit" Enhance>
            <DataAnnotationsValidator />

            <nav class="navbar navbar-expand-lg navbar-light bg-light sticky-top border-bottom shadow-sm mb-4">
                <div class="container-fluid">
                    <ul class="nav nav-pills">
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("employee-section")'
                               @onclick:preventDefault="true">Employee</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("pumps-section")'
                               @onclick:preventDefault="true">Pumps</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("notes-section")'
                               @onclick:preventDefault="true">Notes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("products-section")'
                               @onclick:preventDefault="true">Products</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("taxes-section")'
                               @onclick:preventDefault="true">Taxes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#"
                               @onclick='() => ScrollToSection("cashier-section")'
                               @onclick:preventDefault="true">Cashiers</a>
                        </li>
                    </ul>
                </div>
            </nav>

            @if (validationMessages?.Any() == true)
            {
                <div class="alert alert-danger">
                    <strong>Please fix the following:</strong>
                    <ul class="mb-0">
                        @foreach (var message in validationMessages)
                        {
                            <li>@message</li>
                        }
                    </ul>
                </div>
            }

            <div id="employee-section">
                <!-- Employee Info Table -->
                <h3 class="section-title">Employee Info</h3>

                <input type="hidden" name="EndDayReport.Id" value="@EndDayReport.Id" />
                <div class="input-group mb-3">
                    <span class="input-group-text col-sm-3">Created By</span>
                    <InputText id="createdbyemployeename" @bind-Value="EndDayReport.CreatedByEmployeeName" class="form-control" />
                    <ValidationMessage For="() => EndDayReport.CreatedByEmployeeName" class="text-danger" />
                </div>
                <div class="input-group mb-3">
                    <span class="input-group-text col-sm-3">Created Time</span>
                    <InputDate id="createdat" @bind-Value="EndDayReport.CreatedDateTime" class="form-control" />
                    <ValidationMessage For="() => EndDayReport.CreatedDateTime" class="text-danger" />
                </div>
                <div class="input-group mb-3">
                    <span class="input-group-text col-sm-3">Shift Start</span>
                    <InputText id="shifttimestart" type="time" @bind-Value="EndDayReport.ShiftTimeStartStr" class="form-control" />
                    <ValidationMessage For="() => EndDayReport.ShiftTimeStartStr" class="text-danger" />
                </div>
                <div class="input-group mb-3">
                    <span class="input-group-text col-sm-3">Shift End</span>
                    <InputText id="shifttimeend" type="time" @bind-Value="EndDayReport.ShiftTimeEndStr" class="form-control" />
                    <ValidationMessage For="() => EndDayReport.ShiftTimeEndStr" class="text-danger" />
                </div>
            </div>

            <!-- Pump Sales Table -->
            <div id="pumps-section" class="container mt-3">
                <h3 class="section-title">Pumps</h3>
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Pump</th>
                            <th>Gallons</th>
                            <th>Dollars</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>K1</td>
                            <td class="text-center">
                                <InputNumber id="pumpK1Gallons" @bind-Value="EndDayReport.PumpK1Gallons" step="0.01" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpK1Gallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpK1Dollars" @bind-Value="EndDayReport.PumpK1Dollars" step="0.01" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpK1Dollars" class="text-danger" />

                            </td>
                        </tr>
                        <tr>
                            <td>Off Road Diesel</td>
                            <td class="text-center">
                                <InputNumber id="pumpoffroaddieselgallons" @bind-Value="EndDayReport.PumpOffRoadDieselGallons" min="0" step="0.01" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpOffRoadDieselGallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpoffroaddieseldollars" @bind-Value="EndDayReport.PumpOffRoadDieselDollars" min="0" step="0.01" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpOffRoadDieselDollars" class="text-danger" />
                            </td>
                        </tr>
                        <tr>
                            <td>Unleaded Premium</td>
                            <td class="text-center">
                                <InputNumber id="pumpunleadedpremiumgallons" @bind-Value="EndDayReport.PumpUnleadedPremiumGallons" min="0" step="0.01" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedPremiumGallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpunleadedpremiumdollars" @bind-Value="EndDayReport.PumpUnleadedPremiumDollars" min="0" step="0.01" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedPremiumDollars" class="text-danger" />
                            </td>
                        </tr>
                        <tr>
                            <td>Unleaded Plus</td>
                            <td class="text-center">
                                <InputNumber id="pumpunleadedplusgallons" @bind-Value="EndDayReport.PumpUnleadedPlusGallons" min="0" step="0.01" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedPlusGallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpunleadedplusdollars" @bind-Value="EndDayReport.PumpUnleadedPlusDollars" min="0" step="0.01" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedPlusDollars" class="text-danger" />
                            </td>
                        </tr>
                        <tr>
                            <td>Unleaded Regular</td>
                            <td class="text-center">
                                <InputNumber id="pumpunleadedregulargallons" @bind-Value="EndDayReport.PumpUnleadedRegularGallons" min="0" step="0.01" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedRegularGallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpunleadedregulardollars" @bind-Value="EndDayReport.PumpUnleadedRegularDollars" min="0" step="0.01" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpUnleadedRegularDollars" class="text-danger" />
                            </td>
                        </tr>
                        <tr>
                            <td>Diesel</td>
                            <td class="text-center">
                                <InputNumber id="pumpdieselgallons" @bind-Value="EndDayReport.PumpDieselGallons" step="0.01" min="0" class="form-control" />
                                <ValidationMessage For="() => EndDayReport.PumpDieselGallons" class="text-danger" />
                            </td>
                            <td class="text-end">
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">$</span>
                                    <InputNumber id="pumpdieseldollars" @bind-Value="EndDayReport.PumpDieselDollars" step="0.01" min="0" class="form-control" />
                                </div>
                                <ValidationMessage For="() => EndDayReport.PumpDieselDollars" class="text-danger" />
                            </td>
                        </tr>
                        <tr class="fw-bold table-secondary">
                            <td>Sub Total</td>
                            <td>@EndDayReport.TotalPumpGallons</td>
                            <td>@EndDayReport.TotalPumpSales.ToString("C", CultureInfo.GetCultureInfo("en-US")) </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <br />
            <!--Notes Section -->
            <div id="notes-section" class="container mt-3">
                <h3 class="section-title">Additional</h3>
                <div class="mb-3">
                    <label for="notes" class="form-label">Additional Notes</label>
                    <InputTextArea id="notes"
                                   class="form-control"
                                   @bind-Value="EndDayReport.Notes"
                                   rows="5"
                                   placeholder="Enter any additional details here..." />
                    <ValidationMessage For="() => EndDayReport.Notes" class="text-danger" />
                </div>
            </div>

            <!-- Side-by-side layout for Products and Taxes/Credits -->
            <div class="container mt-3">
                <div class="row">
                    <!-- Products Table (Left) -->
                    <div class="col-md-6">
                        <div id="products-section">
                            <h3 class="section-title">Products</h3>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th class="text-end">Dollars</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="align-middle">Ice</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="ice" @bind-Value="EndDayReport.Ice" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Ice" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Beer</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="beer" @bind-Value="EndDayReport.Beer" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Beer" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Groceries</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="groceries" @bind-Value="EndDayReport.Groceries" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Groceries" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Deli</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="deli" @bind-Value="EndDayReport.Deli" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Deli" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Cigarettes</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="cigarettes" @bind-Value="EndDayReport.Cigarettes" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Cigarettes" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Meat</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="meat" @bind-Value="EndDayReport.Meat" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Meat" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">NonFood</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="nonfood" @bind-Value="EndDayReport.NonFood" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.NonFood" class="text-danger" />
                                        </td>
                                    </tr>
                                    <!--
                                    <tr>
                                        <td class="align-middle">Donuts</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="donuts" @bind-Value="EndDayReport.Donuts" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Donuts" class="text-danger" />
                                        </td>
                                    </tr>
                                    -->
                                    <tr>
                                        <td class="align-middle">Gasoline</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <input type="text" class="form-control text-end" style="padding-right: 1.7rem;" value="@EndDayReport.TotalPumpSales" readonly="readonly" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Chicken</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="chicken" @bind-Value="EndDayReport.Chicken" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Chicken" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">HBA</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="hba" @bind-Value="EndDayReport.HBA" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.HBA" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Lotto</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="lotto" @bind-Value="EndDayReport.Lotto" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Lotto" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Pizza</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="pizza" @bind-Value="EndDayReport.Pizza" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Pizza" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">MoneyOrder</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="moneyorder" @bind-Value="EndDayReport.MoneyOrder" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.MoneyOrder" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Lottery</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="lottery" @bind-Value="EndDayReport.Lottery" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Lottery" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">FarmTax</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="farmtax" @bind-Value="EndDayReport.FarmTax" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.FarmTax" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">SalesTax</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="salestax" @bind-Value="EndDayReport.SalesTax" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.SalesTax" class="text-danger" />
                                        </td>
                                    </tr>

                                    <tr class="fw-bold table-secondary"><td>Sub Total</td><td class="text-end">@EndDayReport.TotalProductsSales.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td></tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="cashier-section">
                            <h3 class="section-title">Over/Under</h3>
                            <button type="button" class="btn btn-sm btn-primary mb-2" @onclick="AddOverUnderEntry">Add Cashier</button>
                            <table class="table table-hover mb-4">
                                <thead>
                                    <tr>
                                        <th>Cashier</th>
                                        <th class="text-end">Amount</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var entry in EndDayReport.OverUnderEntries)
                                    {
                                        <tr>
                                            <td>
                                                <InputText @bind-Value="entry.CashierName" class="form-control" />
                                            </td>
                                            <td class="text-end">
                                                <InputNumber @bind-Value="entry.OverAmount" class="form-control text-end" />
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-danger" @onclick="() => RemoveOverUnderEntry(entry)">×</button>
                                            </td>
                                        </tr>
                                    }

                                    @if (EndDayReport.OverUnderEntries.Any())
                                    {
                                        <tr class="fw-bold table-secondary">
                                            <td>Sub Total</td>
                                            <td colspan="3" class="text-end">
                                                @EndDayReport.OverUnderEntries.Sum(e => e.Total).ToString("C", CultureInfo.GetCultureInfo("en-US"))
                                            </td>
                                            <td></td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Taxes and Less Credits Table (Right) -->
                    <div class="col-md-6">
                        <div id="tenders-section">
                            <h3 class="section-title">Tender</h3>
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Credits</th>
                                        <th class="text-end">Dollars</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="align-middle">Credit Card</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="creditcard" @bind-Value="EndDayReport.CreditCard" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.CreditCard" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Debit Card</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="debitcard" @bind-Value="EndDayReport.DebitCard" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.DebitCard" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Food Stamps</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="foodstamps" @bind-Value="EndDayReport.FoodStamps" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.FoodStamps" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Coupons</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="coupons" @bind-Value="EndDayReport.Coupons" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Coupons" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Approved Credit</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="approvedcredit" @bind-Value="EndDayReport.ApprovedCredit" class="form-control text-end" />
                                            </div>
                                            <div class="input-group mb-3">

                                                @if (EndDayReport.ApprovedCredit != 0)
                                                {
                                                    <input type="text" class="form-control" value="@EndDayReport.ApprovedCreditDepositFileName" placeholder="Please Upload Deposit Slip" aria-label="Please Upload Deposit Slip" aria-describedby="button-addon2">
                                                    <label class="btn btn-outline-secondary" for="ApprovedCreditFileInput">Choose File</label>
                                                    <InputFile id="ApprovedCreditFileInput" class="@GetValidationClass(nameof(EndDayReport.ApprovedCreditDepositFileName))" OnChange="UploadApprovedCreditDepositSlip" style="display:none;" />
                                                }
                                            </div>
                                            <div class="input-group mb-3 text-start">
                                                <ValidationMessage For="() => EndDayReport.ApprovedCredit" class="text-danger" />
                                                <ValidationMessage For="() => EndDayReport.ApprovedCreditDepositFileName" class="text-danger" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Paid Outs</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="paidouts" @bind-Value="EndDayReport.PaidOuts" class="form-control text-end" />
                                            </div>
                                            <div class="input-group mb-3">

                                                @if (EndDayReport.PaidOuts != 0)
                                                {
                                                    <input type="text" class="form-control" value="@EndDayReport.PaidOutDepositFileName" placeholder="Please Upload Deposit Slip" aria-label="Please Upload Deposit Slip" aria-describedby="button-addon2">
                                                    <label class="btn btn-outline-secondary" for="paidOutsFileInput">Choose File</label>
                                                    <InputFile id="paidOutsFileInput" OnChange="UploadPaidOutDepositSlip" style="display:none;" />
                                                }
                                            </div>
                                            <div class="input-group mb-3 text-start">
                                                <ValidationMessage For="() => EndDayReport.PaidOuts" class="text-danger" />
                                                <ValidationMessage For="() => EndDayReport.PaidOutDepositFileName" class="text-danger" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Void And Refund</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="voidandrefund" @bind-Value="EndDayReport.VoidAndRefund" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.VoidAndRefund" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">ATM</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="atm" @bind-Value="EndDayReport.ATM" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.ATM" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Telecheck</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="telecheck" @bind-Value="EndDayReport.Telecheck" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.Telecheck" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr class="align-middle">
                                        <td class="align-middle">Cash</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="cash" @bind-Value="EndDayReport.Cash" class="form-control text-end" />
                                            </div>
                                            <div class="input-group mb-3">

                                                @if (EndDayReport.Cash != 0)
                                                {
                                                    <input type="text" class="form-control" value="@EndDayReport.CashDepositFileName" placeholder="Please Upload Deposit Slip" aria-label="Please Upload Deposit Slip" aria-describedby="button-addon2">
                                                    <label class="btn btn-outline-secondary" for="cashFileInput">Choose File</label>
                                                    <InputFile id="cashFileInput" OnChange="UploadCashDepositSlip" style="display:none;" />
                                                }
                                            </div>
                                            <div class="input-group mb-3 text-start">
                                                <ValidationMessage For="() => EndDayReport.Cash" class="text-danger" />
                                                <ValidationMessage For="() => EndDayReport.CashDepositFileName" class="text-danger" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle">Check</td>
                                        <td class="align-middle text-end">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="check" @bind-Value="EndDayReport.Check" class="form-control text-end" />
                                            </div>
                                            <div class="input-group mb-3">

                                                @if (EndDayReport.Check != 0)
                                                {
                                                    <input type="text" class="form-control" value="@EndDayReport.CheckDepositFileName" placeholder="Please Upload Deposit Slip" aria-label="Please Upload Deposit Slip" aria-describedby="button-addon2">
                                                    <label class="btn btn-outline-secondary" for="checkFileInput">Choose File</label>
                                                    <InputFile id="checkFileInput" OnChange="UploadCheckDepositSlip" style="display:none;" />
                                                }
                                            </div>
                                            <div class="input-group mb-3 text-start">
                                                <ValidationMessage For="() => EndDayReport.Check" class="text-danger" />
                                                <ValidationMessage For="() => EndDayReport.CheckDepositFileName" class="text-danger" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="fw-bold table-secondary">
                                        <td>Sub Total</td>
                                        <td class="text-end">@EndDayReport.TotalCredits.ToString("C", CultureInfo.GetCultureInfo("en-US"))</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="taxes-section">
                            <h3 class="section-title w-100">Tax Report</h3>
                            <table class="table table-hover">
                                <thead class="double-height-header">
                                    <tr>
                                        <th class="text-start text-nowrap fw-bold w-10">Name</th>
                                        <th class="text-end text-nowrap fw-bold w-20">Tax-Rate</th>
                                        <th class="text-end text-nowrap fw-bold w-35">Taxable Sales</th>
                                        <th class="text-end text-nowrap fw-bold w-35">Non-Taxable</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="align-middle text-nowrap w-5">Low Tax</td>
                                        <td class="text-end align-middle w-5">@LowTaxRateDisplay</td>
                                        <td class="text-end align-middle w-45">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="check" @bind-Value="EndDayReport.LowTaxTaxableSales" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.LowTaxTaxableSales" class="text-danger" />
                                        </td>
                                        <td class="text-end align-middle text-nowrap w-45">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="check" @bind-Value="EndDayReport.LowTaxNonTaxableSales" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.LowTaxNonTaxableSales" class="text-danger" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="align-middle text-nowrap w-5">High Tax</td>
                                        <td class="text-end align-middle w-5">@HighTaxRateDisplay</td>
                                        <td class="text-end align-middle w-45">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="check" @bind-Value="EndDayReport.HighTaxTaxableSales" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.HighTaxTaxableSales" class="text-danger" />
                                        </td>
                                        <td class="text-end  align-middle w-45">
                                            <div class="input-group">
                                                <span class="input-group-text" id="basic-addon1">$</span>
                                                <InputNumber id="check" @bind-Value="EndDayReport.HighTaxNonTaxableSales" class="form-control text-end" />
                                            </div>
                                            <ValidationMessage For="() => EndDayReport.HighTaxNonTaxableSales" class="text-danger" />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Save</button>
                <a class="btn btn-primary" href="@($"/enddayreports")">Back to List</a>
            </div>
        </EditForm>
    }
</div>

@code {
    [SupplyParameterFromQuery]
    private int Id { get; set; }

    [SupplyParameterFromForm]
    private EndDayReport? EndDayReport { get; set; }
    private bool isLoading = true;
    private List<string> validationMessages = new();
    private EditContext editContext;

    private decimal LowTaxRate { get; set; }
    private decimal HighTaxRate { get; set; }
    private string LowTaxRateDisplay => $"{LowTaxRate:P3}";
    private string HighTaxRateDisplay => $"{HighTaxRate:P3}";


    protected override async Task OnInitializedAsync()
    {
        // Load tax rates from configuration
        LowTaxRate = Configuration.GetValue<decimal>("TaxRates:Low");
        HighTaxRate = Configuration.GetValue<decimal>("TaxRates:High");

        // Load report from database
        using var context = DbFactory.CreateDbContext();
        EndDayReport ??= await context.EndDayReport
            .Include(r => r.OverUnderEntries)
            .FirstOrDefaultAsync(m => m.Id == Id);

        if (EndDayReport is null)
        {
            NavigationManager.NavigateTo("notfound");
            return;
        }

        editContext = new EditContext(EndDayReport);
        isLoading = false; // ✅ THIS LINE lets the page render the form
        StateHasChanged(); // ✅ Add this to ensure the UI updates when `isLoading` changes

    }


    // To protect from overposting attacks, enable the specific properties you want to bind to.
    // For more information, see https://learn.microsoft.com/aspnet/core/blazor/forms/#mitigate-overposting-attacks.
    private async Task UpdateEndDayReport()
    {
        validationMessages.Clear();
        // Proceed with save

        using var context = DbFactory.CreateDbContext();
        context.Attach(EndDayReport!).State = EntityState.Modified;

        try
        {
            await context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!EndDayReportExists(EndDayReport!.Id))
            {
                NavigationManager.NavigateTo("notfound");
            }
            else
            {
                throw;
            }
        }

        NavigationManager.NavigateTo("/enddayreports");
    }

    private bool EndDayReportExists(int id)
    {
        using var context = DbFactory.CreateDbContext();
        return context.EndDayReport.Any(e => e.Id == id);
    }

    void AddOverUnderEntry()
    {
        EndDayReport ??= new EndDayReport { OverUnderEntries = new List<OverUnderEntry>() };
        EndDayReport.OverUnderEntries.Add(new OverUnderEntry());
    }

    void RemoveOverUnderEntry(OverUnderEntry entry)
    {
        if (EndDayReport?.OverUnderEntries != null)
        {
            EndDayReport.OverUnderEntries.Remove(entry);
        }
    }

    private async Task UploadCashDepositSlip(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null && file.ContentType == "application/pdf")
        {
            using var ms = new MemoryStream();
            await file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024).CopyToAsync(ms);
            EndDayReport!.CashDepositSlipPdf = ms.ToArray();
            EndDayReport!.CashDepositFileName = file.Name; // Store the file name for reference
        }
    }

    private async Task UploadPaidOutDepositSlip(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null && file.ContentType == "application/pdf")
        {
            using var ms = new MemoryStream();
            await file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024).CopyToAsync(ms);
            EndDayReport!.PaidOutDepositSlipPdf = ms.ToArray();
            EndDayReport!.PaidOutDepositFileName = file.Name; // Store the file name for reference
        }
    }

    private async Task UploadApprovedCreditDepositSlip(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null && file.ContentType == "application/pdf")
        {
            using var ms = new MemoryStream();
            await file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024).CopyToAsync(ms);
            EndDayReport!.ApprovedCreditDepositSlipPdf = ms.ToArray();
            EndDayReport!.ApprovedCreditDepositFileName = file.Name; // Store the file name for reference
        }
    }

    private async Task UploadCheckDepositSlip(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null && file.ContentType == "application/pdf")
        {
            using var ms = new MemoryStream();
            await file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024).CopyToAsync(ms);
            EndDayReport!.CheckDepositSlipPdf = ms.ToArray();
            EndDayReport!.CheckDepositFileName = file.Name; // Store the file name for reference
        }
    }

    private async Task HandleInvalidSubmit(EditContext context)
    {
        validationMessages = context
            .GetValidationMessages()
            .ToList();

        await JS.InvokeVoidAsync("scrollToTop");
    }


    private string GetValidationClass(string fieldName)
    {
        if (editContext is null) return "";

        var field = new FieldIdentifier(EndDayReport, fieldName);
        return editContext.GetValidationMessages(field).Any() ? "is-invalid" :
               editContext.IsModified(field) ? "is-valid" : "";
    }

    private async Task ScrollToSection(string sectionId)
    {
        await JS.InvokeVoidAsync("scrollToSection", sectionId);
    }
}

<!-- Inline JS at the bottom of the component -->
<script>
    window.scrollToTop = function () {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };


     window.scrollToSection = (id) => {
        const el = document.getElementById(id);
        if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };
</script>

