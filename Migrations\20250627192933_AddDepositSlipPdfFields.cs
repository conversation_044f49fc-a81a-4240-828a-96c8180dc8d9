﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class AddDepositSlipPdfFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<byte[]>(
                name: "CashDepositSlipPdf",
                table: "EndDayReport",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "PaidOutDepositSlipPdf",
                table: "EndDayReport",
                type: "varbinary(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CashDepositSlipPdf",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "PaidOutDepositSlipPdf",
                table: "EndDayReport");
        }
    }
}
