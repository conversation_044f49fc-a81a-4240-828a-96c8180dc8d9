﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class depositSlips2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ApprovedCreditDepositFileName",
                table: "EndDayReport",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "ApprovedCreditDepositSlipPdf",
                table: "EndDayReport",
                type: "varbinary(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CheckDepositFileName",
                table: "EndDayReport",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<byte[]>(
                name: "CheckDepositSlipPdf",
                table: "EndDayReport",
                type: "varbinary(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApprovedCreditDepositFileName",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "ApprovedCreditDepositSlipPdf",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "CheckDepositFileName",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "CheckDepositSlipPdf",
                table: "EndDayReport");
        }
    }
}
