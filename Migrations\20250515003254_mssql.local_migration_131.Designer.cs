﻿// <auto-generated />
using System;
using EndDayReports.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EndDayReports.Migrations
{
    [DbContext(typeof(EndDayReportsContext))]
    [Migration("20250515003254_mssql.local_migration_131")]
    partial class mssqllocal_migration_131
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EndDayReports.model.EndDayReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("ATM")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ApprovedCredit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Beer")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cash")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Check")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Chicken")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cigarettes")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Coupons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByEmployeeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("CreditCard")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Deli")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Donuts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("DebitCard")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FarmTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FoodStamps")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Gasoline")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Groceries")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("HBA")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Ice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Lottery")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Lotto")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Meat")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MoneyOrder")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("NonFood")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PaidOuts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Pizza")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpDieselDollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpDieselGallons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpK1Dollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpK1Gallons")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpOffRoadDieselDollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpOffRoadDieselGallons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedPlusDollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedPlusGallons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedPremiumDollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedPremiumGallons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedRegularDollars")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PumpUnleadedRegularGallons")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("SalesTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<TimeOnly>("ShiftTimeEnd")
                        .HasColumnType("time");

                    b.Property<TimeOnly>("ShiftTimeStart")
                        .HasColumnType("time");

                    b.Property<decimal>("Telecheck")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalCredits")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("VoidAndRefund")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.ToTable("EndDayReport");
                });
#pragma warning restore 612, 618
        }
    }
}
