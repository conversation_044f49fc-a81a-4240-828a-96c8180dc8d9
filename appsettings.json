{
  "DetailedErrors": false,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "EndDayReportsContext": "Server=tcp:alpaca-sql.database.windows.net;Database=Locust-db;User ID=EOD_reportinguser;Password=********************;MultipleActiveResultSets=true;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "EndDayReportsDevContext": "Server=(localdb)\\mssqllocaldb;Database=EndDayReportsContext-62c2c150-cf71-42bc-acf7-a970b9ec52b4;Trusted_Connection=True;MultipleActiveResultSets=true"
  },
  // ...other settings...
  "TaxRates": {
    "Low": 0.0547,
    "High": 0.08475
  }
}