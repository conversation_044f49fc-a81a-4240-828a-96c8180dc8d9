﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using EndDayReports.model;

namespace EndDayReports.Data
{
    public class EndDayReportsContext : DbContext
    {
        public EndDayReportsContext (DbContextOptions<EndDayReportsContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OverUnderEntry>()
                .<PERSON><PERSON><PERSON>(x => x.Id);
        }


        public DbSet<EndDayReports.model.EndDayReport> EndDayReport { get; set; } = default!;
    }
}
