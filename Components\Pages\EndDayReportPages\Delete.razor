@page "/enddayreports/delete"
@using Microsoft.EntityFrameworkCore
@using EndDayReports.model
@inject IDbContextFactory<EndDayReports.Data.EndDayReportsContext> DbFactory
@inject NavigationManager NavigationManager

<PageTitle>Delete</PageTitle>

<h1>Delete Report</h1>

<p>Are you sure you want to delete this?</p>
<div>
    @if (enddayreport is null)
    {
        <p><em>Loading...</em></p>
    }
    else {
        <dl class="row">
            <dt class="col-sm-2">Created By</dt>
            <dd class="col-sm-10">@enddayreport.CreatedByEmployeeName</dd>
        </dl>
        <dl class="row">
            <dt class="col-sm-2">Created</dt>
            <dd class="col-sm-10">@enddayreport.CreatedDateTime</dd>
        </dl>
        <dl class="row">
            <dt class="col-sm-2">Shift Time Start</dt>
            <dd class="col-sm-10">@enddayreport.ShiftTimeStart</dd>
        </dl>
        <dl class="row">
            <dt class="col-sm-2">Shift Time End</dt>
            <dd class="col-sm-10">@enddayreport.ShiftTimeEnd</dd>
        </dl>
        
        <dl class="row">
            <dt class="col-sm-2">Total Credits</dt>
            <dd class="col-sm-10">@enddayreport.TotalProductsSales</dd>
        </dl>

        <EditForm method="post" Model="enddayreport" OnValidSubmit="DeleteEndDayReport" FormName="delete" Enhance>
           <div class="mt-4">
            <button type="submit" class="btn btn-danger" disabled="@(enddayreport is null)">Delete</button> 
            <a class="btn btn-primary" href="@($"/enddayreports")">Back to List</a>
           </div>
        </EditForm>
    }
</div>

@code {
    private EndDayReport? enddayreport;

    [SupplyParameterFromQuery]
    private int Id { get; set; }

    protected override async Task OnInitializedAsync()
    {
        using var context = DbFactory.CreateDbContext();
        enddayreport = await context.EndDayReport.FirstOrDefaultAsync(m => m.Id == Id);

        if (enddayreport is null)
        {
            NavigationManager.NavigateTo("notfound");
        }
    }

    private async Task DeleteEndDayReport()
    {
        using var context = DbFactory.CreateDbContext();
        context.EndDayReport.Remove(enddayreport!);
        await context.SaveChangesAsync();
        NavigationManager.NavigateTo("/enddayreports");
    }
}
