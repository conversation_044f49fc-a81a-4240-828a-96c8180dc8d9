﻿/*.quickgrid thead tr:first-child {
    background-color: black;
    color: white;
}*/

.quickgrid thead {
    background-color: #003366;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
}

::deep th.country-name {
    width: 14rem;
}

/* Subtle stripe effect */
/*::deep tbody tr:nth-child(even) {
    background: rgb(128, 128, 128);
}*/

/* Don't collapse rows even if they are empty */
::deep tbody tr {
    height: 1.8rem;
}



/* Index.razor.css */
.title {
    color: red;
}

/* Index.razor.css */
.highlight {
    background-color: yellow;
    padding: 1rem;
}