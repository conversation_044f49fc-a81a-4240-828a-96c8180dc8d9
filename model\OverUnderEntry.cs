﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace EndDayReports.model
{
    [Table("OverUnderEntrys", Schema = "EoD")]
    public class OverUnderEntry
    {
        public int Id { get; set; } // Primary key required by EF Core

        public string CashierName { get; set; } = string.Empty;

        [Precision(18, 2)]
        public decimal OverAmount { get; set; }
               
        public decimal Total => OverAmount;

        // Optional: navigation back to parent
        public int EndDayReportId { get; set; }
        public EndDayReport EndDayReport { get; set; } = null!;
    }

}
