﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class addCasherOver : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OverUnderEntry",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CashierName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OverAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    UnderAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EndDayReportId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OverUnderEntry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OverUnderEntry_EndDayReport_EndDayReportId",
                        column: x => x.EndDayReportId,
                        principalTable: "EndDayReport",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OverUnderEntry_EndDayReportId",
                table: "OverUnderEntry",
                column: "EndDayReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OverUnderEntry");
        }
    }
}
