﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EndDayReports.model; 
using EndDayReports.Data;   

namespace EndDayReports.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReportsController : ControllerBase
    {
        private readonly EndDayReportsContext _context;

        public ReportsController(EndDayReportsContext context)
        {
            _context = context;
       
        }

        [HttpGet("{id}/download/cash")]
        public async Task<IActionResult> DownloadCashDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.CashDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.CashDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.CashDepositFileName ?? $"cash-deposit-{id}.pdf"
            );
        }

        [HttpGet("{id}/download/paidout")]
        public async Task<IActionResult> DownloadPaidOutDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.PaidOutDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.PaidOutDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.PaidOutDepositFileName ?? $"paidout-deposit-{id}.pdf"
            );
        }


        [HttpGet("{id}/download/approvedcredit")]
        public async Task<IActionResult> DownloadApprovedCreditDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.ApprovedCreditDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.ApprovedCreditDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.ApprovedCreditDepositFileName ?? $"approvedcredit-deposit-{id}.pdf"
            );
        }


        [HttpGet("{id}/download/check")]
        public async Task<IActionResult> DownloadCheckDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.CheckDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.CheckDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.CashDepositFileName ?? $"cash-deposit-{id}.pdf"
            );
        }
    }
}
