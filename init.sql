﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [EndDayReport] (
    [Id] int NOT NULL IDENTITY,
    [CreatedByEmployeeName] nvarchar(max) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [ShiftTimeStart] time NOT NULL,
    [ShiftTimeEnd] time NOT NULL,
    [PumpK1Dollars] decimal(18,2) NOT NULL,
    [PumpK1Gallons] decimal(18,2) NOT NULL,
    [PumpOffRoadDieselDollars] decimal(18,2) NOT NULL,
    [PumpOffRoadDieselGallons] decimal(18,2) NOT NULL,
    [PumpUnleadedPremiumDollars] decimal(18,2) NOT NULL,
    [PumpUnleadedPremiumGallons] decimal(18,2) NOT NULL,
    [PumpUnleadedPlusDollars] decimal(18,2) NOT NULL,
    [PumpUnleadedPlusGallons] decimal(18,2) NOT NULL,
    [PumpUnleadedRegularDollars] decimal(18,2) NOT NULL,
    [PumpUnleadedRegularGallons] decimal(18,2) NOT NULL,
    [PumpDieselDollars] decimal(18,2) NOT NULL,
    [PumpDieselGallons] decimal(18,2) NOT NULL,
    [Ice] decimal(18,2) NOT NULL,
    [Beer] decimal(18,2) NOT NULL,
    [Groceries] decimal(18,2) NOT NULL,
    [Deli] decimal(18,2) NOT NULL,
    [Cigarettes] decimal(18,2) NOT NULL,
    [Meat] decimal(18,2) NOT NULL,
    [NonFood] decimal(18,2) NOT NULL,
    [Donuts] decimal(18,2) NOT NULL,
    [Gasoline] decimal(18,2) NOT NULL,
    [Chicken] decimal(18,2) NOT NULL,
    [HBA] decimal(18,2) NOT NULL,
    [Lotto] decimal(18,2) NOT NULL,
    [Pizza] decimal(18,2) NOT NULL,
    [MoneyOrder] decimal(18,2) NOT NULL,
    [Lottery] decimal(18,2) NOT NULL,
    [FarmTax] decimal(18,2) NOT NULL,
    [SalesTax] decimal(18,2) NOT NULL,
    [CreditCard] decimal(18,2) NOT NULL,
    [EBT] decimal(18,2) NOT NULL,
    [FoodStamps] decimal(18,2) NOT NULL,
    [Coupons] decimal(18,2) NOT NULL,
    [ApprovedCredit] decimal(18,2) NOT NULL,
    [PaidOuts] decimal(18,2) NOT NULL,
    [VoidAndRefund] decimal(18,2) NOT NULL,
    [ATM] decimal(18,2) NOT NULL,
    [Telecheck] decimal(18,2) NOT NULL,
    [Cash] decimal(18,2) NOT NULL,
    [Check] decimal(18,2) NOT NULL,
    [TotalCredits] decimal(18,2) NOT NULL,
    CONSTRAINT [PK_EndDayReport] PRIMARY KEY ([Id])
);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250515003254_mssql.local_migration_131', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

DECLARE @var0 sysname;
SELECT @var0 = [d].[name]
FROM [sys].[default_constraints] [d]
INNER JOIN [sys].[columns] [c] ON [d].[parent_column_id] = [c].[column_id] AND [d].[parent_object_id] = [c].[object_id]
WHERE ([d].[parent_object_id] = OBJECT_ID(N'[EndDayReport]') AND [c].[name] = N'TotalCredits');
IF @var0 IS NOT NULL EXEC(N'ALTER TABLE [EndDayReport] DROP CONSTRAINT [' + @var0 + '];');
ALTER TABLE [EndDayReport] DROP COLUMN [TotalCredits];
GO

EXEC sp_rename N'[EndDayReport].[CreatedAt]', N'CreatedDateTime', N'COLUMN';
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250515132201_CreatedDateTime', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [EndDayReport] ADD [ShiftTimeEndStr] nvarchar(max) NOT NULL DEFAULT N'';
GO

ALTER TABLE [EndDayReport] ADD [ShiftTimeStartStr] nvarchar(max) NOT NULL DEFAULT N'';
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250521150106_20250520_ShiftStartTimeStr', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

EXEC sp_rename N'[EndDayReport].[EBT]', N'DebitCard', N'COLUMN';
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627153718_RenameEbtToDebitCard', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [EndDayReport] ADD [HighTaxNonTaxableSales] decimal(18,2) NOT NULL DEFAULT 0.0;
GO

ALTER TABLE [EndDayReport] ADD [HighTaxTaxableSales] decimal(18,2) NOT NULL DEFAULT 0.0;
GO

ALTER TABLE [EndDayReport] ADD [LowTaxNonTaxableSales] decimal(18,2) NOT NULL DEFAULT 0.0;
GO

ALTER TABLE [EndDayReport] ADD [LowTaxTaxableSales] decimal(18,2) NOT NULL DEFAULT 0.0;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627155408_addTaxableSalesSection', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [OverUnderEntry] (
    [Id] int NOT NULL IDENTITY,
    [CashierName] nvarchar(max) NOT NULL,
    [OverAmount] decimal(18,2) NOT NULL,
    [UnderAmount] decimal(18,2) NOT NULL,
    [EndDayReportId] int NOT NULL,
    CONSTRAINT [PK_OverUnderEntry] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_OverUnderEntry_EndDayReport_EndDayReportId] FOREIGN KEY ([EndDayReportId]) REFERENCES [EndDayReport] ([Id]) ON DELETE CASCADE
);
GO

CREATE INDEX [IX_OverUnderEntry_EndDayReportId] ON [OverUnderEntry] ([EndDayReportId]);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627175100_addCasherOver', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627175255_SetPrecisionOnOverUnder', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [EndDayReport] ADD [CashDepositSlipPdf] varbinary(max) NULL;
GO

ALTER TABLE [EndDayReport] ADD [PaidOutDepositSlipPdf] varbinary(max) NULL;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627192933_AddDepositSlipPdfFields', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [EndDayReport] ADD [CashDepositFileName] nvarchar(max) NULL;
GO

ALTER TABLE [EndDayReport] ADD [PaidOutDepositFileName] nvarchar(max) NULL;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250627201951_addFilePaths', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [EndDayReport] ADD [ApprovedCreditDepositFileName] nvarchar(max) NULL;
GO

ALTER TABLE [EndDayReport] ADD [ApprovedCreditDepositSlipPdf] varbinary(max) NULL;
GO

ALTER TABLE [EndDayReport] ADD [CheckDepositFileName] nvarchar(max) NULL;
GO

ALTER TABLE [EndDayReport] ADD [CheckDepositSlipPdf] varbinary(max) NULL;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250708131206_depositSlips2', N'8.0.11');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [OverUnderEntry] DROP CONSTRAINT [FK_OverUnderEntry_EndDayReport_EndDayReportId];
GO

ALTER TABLE [OverUnderEntry] DROP CONSTRAINT [PK_OverUnderEntry];
GO

ALTER TABLE [EndDayReport] DROP CONSTRAINT [PK_EndDayReport];
GO

IF SCHEMA_ID(N'EoD') IS NULL EXEC(N'CREATE SCHEMA [EoD];');
GO

EXEC sp_rename N'[OverUnderEntry]', N'OverUnderEntrys';
ALTER SCHEMA [EoD] TRANSFER [OverUnderEntrys];
GO

EXEC sp_rename N'[EndDayReport]', N'EndDayReports';
ALTER SCHEMA [EoD] TRANSFER [EndDayReports];
GO

EXEC sp_rename N'[EoD].[OverUnderEntrys].[IX_OverUnderEntry_EndDayReportId]', N'IX_OverUnderEntrys_EndDayReportId', N'INDEX';
GO

ALTER TABLE [EoD].[OverUnderEntrys] ADD CONSTRAINT [PK_OverUnderEntrys] PRIMARY KEY ([Id]);
GO

ALTER TABLE [EoD].[EndDayReports] ADD CONSTRAINT [PK_EndDayReports] PRIMARY KEY ([Id]);
GO

ALTER TABLE [EoD].[OverUnderEntrys] ADD CONSTRAINT [FK_OverUnderEntrys_EndDayReports_EndDayReportId] FOREIGN KEY ([EndDayReportId]) REFERENCES [EoD].[EndDayReports] ([Id]) ON DELETE CASCADE;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250708162614_InitReportingSchema', N'8.0.11');
GO

COMMIT;
GO

