﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class mssqllocal_migration_131 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EndDayReport",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedByEmployeeName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ShiftTimeStart = table.Column<TimeOnly>(type: "time", nullable: false),
                    ShiftTimeEnd = table.Column<TimeOnly>(type: "time", nullable: false),
                    PumpK1Dollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpK1Gallons = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    PumpOffRoadDieselDollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpOffRoadDieselGallons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedPremiumDollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedPremiumGallons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedPlusDollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedPlusGallons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedRegularDollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpUnleadedRegularGallons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpDieselDollars = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PumpDieselGallons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Ice = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Beer = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Groceries = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Deli = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Cigarettes = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Meat = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    NonFood = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Donuts = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Gasoline = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Chicken = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    HBA = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Lotto = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Pizza = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    MoneyOrder = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Lottery = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FarmTax = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    SalesTax = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CreditCard = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    EBT = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    FoodStamps = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Coupons = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ApprovedCredit = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    PaidOuts = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    VoidAndRefund = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ATM = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Telecheck = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Cash = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Check = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TotalCredits = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EndDayReport", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EndDayReport");
        }
    }
}
