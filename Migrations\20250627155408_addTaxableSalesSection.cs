﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EndDayReports.Migrations
{
    /// <inheritdoc />
    public partial class addTaxableSalesSection : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "HighTaxNonTaxableSales",
                table: "EndDayReport",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "HighTaxTaxableSales",
                table: "EndDayReport",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "LowTaxNonTaxableSales",
                table: "EndDayReport",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "LowTaxTaxableSales",
                table: "EndDayReport",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HighTaxNonTaxableSales",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "HighTaxTaxableSales",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "LowTaxNonTaxableSales",
                table: "EndDayReport");

            migrationBuilder.DropColumn(
                name: "LowTaxTaxableSales",
                table: "EndDayReport");
        }
    }
}
