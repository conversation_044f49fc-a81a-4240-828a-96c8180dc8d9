{"$schema": "https://schema.management.azure.com/schemas/2018-05-01/subscriptionDeploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_dependencyType": "compute.appService.container"}, "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "EndDayReports", "metadata": {"description": "Name of the resource group for the resource. It is recommended to put resources under same resource group for better tracking."}}, "resourceGroupLocation": {"type": "string", "defaultValue": "canadacentral", "metadata": {"description": "Location of the resource group. Resource groups could have different location than resources, however by default we use API versions from latest hybrid profile which support all locations for resource types we support."}}, "resourceName": {"type": "string", "defaultValue": "EndDayReports20250707151440", "metadata": {"description": "Name of the main resource to be created by this template."}}, "resourceLocation": {"type": "string", "defaultValue": "[parameters('resourceGroupLocation')]", "metadata": {"description": "Location of the resource. By default use resource group's location, unless the resource provider is not supported there."}}}, "variables": {"appServicePlan_name": "[concat('Plan', uniqueString(concat(parameters('resourceName'), subscription().subscriptionId)))]", "appServicePlan_ResourceId": "[concat('/subscriptions/', subscription().subscriptionId, '/resourceGroups/', parameters('resourceGroupName'), '/providers/Microsoft.Web/serverFarms/', variables('appServicePlan_name'))]", "appServiceContainer_name": "['Container', uniqueString(concat(parameters('resourceName'), subscription().subscriptionId)))]"}, "resources": [{"type": "Microsoft.Resources/resourceGroups", "name": "[parameters('resourceGroupName')]", "location": "[parameters('resourceGroupLocation')]", "apiVersion": "2019-10-01"}, {"type": "Microsoft.Resources/deployments", "name": "[concat(parameters('resourceGroupName'), 'Deployment', uniqueString(concat(parameters('resourceName'), subscription().subscriptionId)))]", "resourceGroup": "[parameters('resourceGroupName')]", "apiVersion": "2019-10-01", "dependsOn": ["[parameters('resourceGroupName')]"], "properties": {"mode": "Incremental", "template": {"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "resources": [{"location": "[parameters('resourceLocation')]", "name": "[parameters('resourceName')]", "type": "Microsoft.Web/sites", "apiVersion": "2015-08-01", "tags": {"[concat('hidden-related:', variables('appServicePlan_ResourceId'))]": "empty"}, "dependsOn": ["[variables('appServicePlan_ResourceId')]"], "kind": "app", "properties": {"name": "[parameters('resourceName')]", "kind": "app", "httpsOnly": true, "reserved": false, "serverFarmId": "[variables('appServicePlan_ResourceId')]", "siteConfig": {"linuxFxVersion": "DOCKER|nginx"}}, "identity": {"type": "SystemAssigned"}}, {"location": "[parameters('resourceLocation')]", "name": "[variables('appServicePlan_name')]", "type": "Microsoft.Web/serverFarms", "apiVersion": "2015-02-01", "kind": "linux", "properties": {"name": "[variables('appServicePlan_name')]", "sku": "Standard", "workerSizeId": "0", "reserved": true}}, {"location": "[parameters('resourceLocation')]", "name": "[variables('appServiceContainer_name')]", "type": "Microsoft.ContainerRegistry/registries", "apiVersion": "2017-10-01", "sku": {"name": "Standard"}, "properties": {"tenantId": "[subscription().tenantId]", "adminUserEnabled": false}}]}}}]}